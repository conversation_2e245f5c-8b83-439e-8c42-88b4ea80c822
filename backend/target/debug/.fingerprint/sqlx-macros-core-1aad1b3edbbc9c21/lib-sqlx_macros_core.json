{"rustc": 5357548097637079788, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 1075730617736146034, "deps": [[530211389790465181, "hex", false, 16324847589253174832], [996810380461694889, "sqlx_core", false, 8843800358365232445], [1441306149310335789, "tempfile", false, 9494009109866439715], [2713742371683562785, "syn", false, 14198818739518330088], [3060637413840920116, "proc_macro2", false, 14159985505816259843], [3150220818285335163, "url", false, 15651950450722999137], [3405707034081185165, "dotenvy", false, 6053029920451363121], [3722963349756955755, "once_cell", false, 6377235261242616408], [8045585743974080694, "heck", false, 4276597429369071501], [9689903380558560274, "serde", false, 14996443586811812500], [9857275760291862238, "sha2", false, 11483536383405105607], [11838249260056359578, "sqlx_sqlite", false, 4695982689955074002], [12170264697963848012, "either", false, 17903523402819855491], [16362055519698394275, "serde_json", false, 13105226277041243466], [17531218394775549125, "tokio", false, 12197225397187772990], [17990358020177143287, "quote", false, 12794325613567333191]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-1aad1b3edbbc9c21/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
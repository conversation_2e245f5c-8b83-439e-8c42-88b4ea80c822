{"rustc": 5357548097637079788, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 8276155916380437441, "path": 6406626505112543966, "deps": [[555019317135488525, "regex_automata", false, 15096080750253833279], [2779309023524819297, "aho_corasick", false, 12876503838924517814], [9408802513701742484, "regex_syntax", false, 17732951718237019335], [15932120279885307830, "memchr", false, 402131329472918033]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-52dec6d719b9b524/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2210360036522109082, "path": 1045956482866905453, "deps": [[4684437522915235464, "libc", false, 6962756435946676927], [7896293946984509699, "bitflags", false, 13644053219853896423], [8253628577145923712, "libc_errno", false, 13023673104419925337], [10004434995811528692, "build_script_build", false, 15816644776510351733]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-c21effd7fc25f4c9/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
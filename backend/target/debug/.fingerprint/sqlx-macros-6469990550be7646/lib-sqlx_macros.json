{"rustc": 5357548097637079788, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 12295128238091880824, "deps": [[996810380461694889, "sqlx_core", false, 8843800358365232445], [2713742371683562785, "syn", false, 14198818739518330088], [3060637413840920116, "proc_macro2", false, 14159985505816259843], [15733334431800349573, "sqlx_macros_core", false, 14971318588860689680], [17990358020177143287, "quote", false, 12794325613567333191]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-6469990550be7646/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
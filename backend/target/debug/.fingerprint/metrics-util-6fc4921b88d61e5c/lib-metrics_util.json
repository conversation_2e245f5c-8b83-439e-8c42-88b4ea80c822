{"rustc": 5357548097637079788, "features": "[\"crossbeam-epoch\", \"crossbeam-utils\", \"handles\", \"hashbrown\", \"num_cpus\", \"quanta\", \"recency\", \"registry\", \"sketches-ddsketch\", \"summary\"]", "declared_features": "[\"ahash\", \"aho-corasick\", \"crossbeam-epoch\", \"crossbeam-utils\", \"debugging\", \"default\", \"handles\", \"hashbrown\", \"indexmap\", \"layer-filter\", \"layer-router\", \"layers\", \"num_cpus\", \"ordered-float\", \"quanta\", \"radix_trie\", \"recency\", \"registry\", \"sketches-ddsketch\", \"summary\"]", "target": 13028150694916642802, "profile": 8276155916380437441, "path": 16290672989564901470, "deps": [[2357570525450087091, "num_cpus", false, 6695656246939100690], [3528074118530651198, "crossbeam_epoch", false, 10486221908099123840], [4468123440088164316, "crossbeam_utils", false, 5764622209327980301], [5488147963494030863, "metrics", false, 13826394748114833717], [6800597322617244483, "quanta", false, 16242660967640849105], [11104214393329361678, "hashbrown", false, 5860240053231828651], [16687810213102005944, "sketches_ddsketch", false, 6313757442093980408]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/metrics-util-6fc4921b88d61e5c/dep-lib-metrics_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
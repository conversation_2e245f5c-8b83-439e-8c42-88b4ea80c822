{"rustc": 5357548097637079788, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 3033921117576893, "path": 3788050122530589305, "deps": [[5103565458935487, "futures_io", false, 1068903677632396460], [40386456601120721, "percent_encoding", false, 17998032803782996755], [530211389790465181, "hex", false, 16324847589253174832], [788558663644978524, "crossbeam_queue", false, 14502483625610802234], [966925859616469517, "ahash", false, 12721873881805741287], [1162433738665300155, "crc", false, 14866548125622867928], [1464803193346256239, "event_listener", false, 1129611558963035758], [1811549171721445101, "futures_channel", false, 32965724446531131], [3150220818285335163, "url", false, 15651950450722999137], [3405817021026194662, "hashlink", false, 5878071727355734193], [3646857438214563691, "futures_intrusive", false, 10142671982086622237], [3666196340704888985, "smallvec", false, 10210049263245705491], [3712811570531045576, "byteorder", false, 8349658801979576102], [3722963349756955755, "once_cell", false, 6377235261242616408], [5986029879202738730, "log", false, 7767284336163733795], [6493259146304816786, "indexmap", false, 2242892611830683830], [7620660491849607393, "futures_core", false, 17617314577359120371], [8008191657135824715, "thiserror", false, 16347307249593682823], [8319709847752024821, "uuid", false, 8647608891752482920], [8606274917505247608, "tracing", false, 4776999656597797870], [9689903380558560274, "serde", false, 14996443586811812500], [9857275760291862238, "sha2", false, 11483536383405105607], [9897246384292347999, "chrono", false, 11063610827689129599], [10629569228670356391, "futures_util", false, 10291020257623253360], [10862088793507253106, "sqlformat", false, 8225852479879718117], [11295624341523567602, "rustls", false, 13051887522308603832], [12170264697963848012, "either", false, 17903523402819855491], [15932120279885307830, "memchr", false, 14939884226391565787], [16066129441945555748, "bytes", false, 7934153550148054855], [16311359161338405624, "rustls_pemfile", false, 8011052194259419456], [16362055519698394275, "serde_json", false, 13105226277041243466], [16973251432615581304, "tokio_stream", false, 1952259539451003103], [17106256174509013259, "atoi", false, 11022960237486739611], [17531218394775549125, "tokio", false, 12197225397187772990], [17605717126308396068, "paste", false, 7496892716578168760], [17652733826348741533, "webpki_roots", false, 15711084223099909596]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-3c60501645a0ecba/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}